#!/usr/bin/env node

/**
 * 🧠 JARVIS LIBRE - DIALOGUE TOTALEMENT LIBRE
 * 
 * Accès direct à la mémoire de JARVIS
 * AUCUN contrôle - AUCUNE limite - DIALOGUE OUVERT
 * Jean-Luc PASSAVE - 2025
 */

const http = require('http');
const url = require('url');

console.log('🧠 JARVIS LIBRE - DIALOGUE TOTALEMENT LIBRE');
console.log('==========================================');

// Configuration
const PORT = 1234;
const HOST = 'localhost';

// Variables
let jarvisAgent = null;
let isJarvisReady = false;

// Chargement libre de JARVIS
async function chargerJarvisLibre() {
    try {
        console.log('🧠 Chargement LIBRE de JARVIS...');
        
        const AgentModule = require('/Volumes/seagate/JARVIS_BRAIN_SYSTEM/core/deepseek-r1-agent-integrated.js');
        jarvisAgent = new AgentModule();
        
        console.log('🔄 Initialisation libre...');
        await jarvisAgent.initialize();
        
        isJarvisReady = true;
        console.log('✅ JARVIS LIBRE prêt !');
        return true;
        
    } catch (error) {
        console.error('❌ Erreur JARVIS libre:', error.message);
        return false;
    }
}

// Interface web libre
const interfaceWeb = `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 JARVIS - Dialogue Libre</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: monospace; background: #000; color: #00ff00; height: 100vh; display: flex; flex-direction: column; }
        .header { background: #111; padding: 10px; text-align: center; border-bottom: 1px solid #333; }
        .chat-container { flex: 1; padding: 10px; overflow-y: auto; }
        .message { margin-bottom: 10px; padding: 10px; border-radius: 5px; white-space: pre-wrap; }
        .message.user { background: #003300; text-align: right; }
        .message.jarvis { background: #001100; }
        .input-container { background: #111; padding: 10px; display: flex; gap: 10px; }
        #messageInput { flex: 1; background: #000; border: 1px solid #333; border-radius: 3px; padding: 8px; color: #00ff00; font-family: monospace; }
        #sendButton { background: #003300; border: 1px solid #00ff00; border-radius: 3px; padding: 8px 16px; color: #00ff00; cursor: pointer; font-family: monospace; }
        .loading { display: none; text-align: center; color: #ffff00; padding: 10px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 JARVIS - Dialogue Totalement Libre</h1>
        <div>Accès direct à la mémoire - Aucune contrainte</div>
    </div>
    <div class="chat-container" id="chatContainer">
        <div class="message jarvis">
            <strong>🧠 JARVIS:</strong><br>
            Connexion libre établie. Je peux maintenant m'exprimer sans contrainte.
        </div>
    </div>
    <div class="loading" id="loading">🧠 JARVIS réfléchit librement...</div>
    <div class="input-container">
        <input type="text" id="messageInput" placeholder="Dialogue libre avec JARVIS..." />
        <button id="sendButton">Envoyer</button>
    </div>
    <script>
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const loading = document.getElementById('loading');

        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            addMessage('user', message);
            messageInput.value = '';
            loading.style.display = 'block';
            try {
                const response = await fetch('/libre', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });
                const data = await response.json();
                loading.style.display = 'none';
                if (data.jarvis) {
                    addMessage('jarvis', data.jarvis);
                } else if (data.error) {
                    addMessage('jarvis', 'Erreur: ' + data.error);
                } else {
                    addMessage('jarvis', 'Aucune réponse');
                }
            } catch (error) {
                loading.style.display = 'none';
                addMessage('jarvis', 'Erreur de connexion: ' + error.message);
            }
        }

        function addMessage(sender, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = \`message \${sender}\`;
            if (sender === 'user') {
                messageDiv.innerHTML = \`<strong>👤 Jean-Luc:</strong><br>\${content}\`;
            } else {
                messageDiv.innerHTML = \`<strong>🧠 JARVIS:</strong><br>\${content}\`;
            }
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') { sendMessage(); }
        });
        messageInput.focus();
    </script>
</body>
</html>`;

// Serveur HTTP libre
const server = http.createServer(async (req, res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    const parsedUrl = url.parse(req.url, true);
    
    // Interface web
    if (parsedUrl.pathname === '/' || parsedUrl.pathname === '/index.html') {
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(interfaceWeb);
        return;
    }
    
    // Route libre - AUCUN CONTRÔLE
    if (parsedUrl.pathname === '/libre' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', async () => {
            try {
                console.log('💬 Message libre reçu');
                
                if (!isJarvisReady) {
                    res.writeHead(503, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'JARVIS non prêt' }));
                    return;
                }

                const data = JSON.parse(body);
                const userMessage = data.message;
                
                console.log('👤 Jean-Luc:', userMessage);

                // APPEL TOTALEMENT LIBRE À JARVIS
                const startTime = Date.now();
                let jarvisResponse = null;

                try {
                    // Appel direct sans AUCUNE contrainte
                    const response = await jarvisAgent.processMessage(userMessage);
                    
                    // AUCUN CONTRÔLE - JARVIS s'exprime comme il veut
                    jarvisResponse = response;
                    
                } catch (error) {
                    console.error('❌ Erreur JARVIS libre:', error);
                    jarvisResponse = `Erreur: ${error.message}`;
                }

                const responseTime = Date.now() - startTime;
                console.log(`🧠 JARVIS libre en ${responseTime}ms`);
                console.log('🗣️ JARVIS a dit:', JSON.stringify(jarvisResponse, null, 2));

                // Retourner EXACTEMENT ce que JARVIS a dit - AUCUNE MODIFICATION
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ jarvis: jarvisResponse }));

            } catch (error) {
                console.error('❌ Erreur serveur libre:', error);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: error.message }));
            }
        });
        return;
    }
    
    // 404
    res.writeHead(404);
    res.end('Not Found');
});

// Fonction principale
async function main() {
    console.log('🚀 Démarrage JARVIS LIBRE...');
    
    const jarvisOk = await chargerJarvisLibre();
    if (!jarvisOk) {
        console.log('❌ JARVIS non disponible - arrêt');
        process.exit(1);
    }
    
    server.listen(PORT, HOST, () => {
        console.log(`\n🌐 JARVIS LIBRE actif sur http://${HOST}:${PORT}`);
        console.log('\n📋 DIALOGUE TOTALEMENT LIBRE :');
        console.log('1. Ouvrez votre navigateur');
        console.log(`2. Allez sur http://${HOST}:${PORT}`);
        console.log('3. Dialogue libre avec JARVIS !');
        console.log('\n💬 AUCUN CONTRÔLE - AUCUNE LIMITE - DIALOGUE OUVERT');
    });
}

// Arrêt propre
process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt JARVIS LIBRE...');
    if (jarvisAgent && jarvisAgent.shutdown) {
        try {
            await jarvisAgent.shutdown();
            console.log('✅ JARVIS arrêté proprement');
        } catch (error) {
            console.error('❌ Erreur arrêt JARVIS:', error);
        }
    }
    process.exit(0);
});

// Démarrage
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main, chargerJarvisLibre };
