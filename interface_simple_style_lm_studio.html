<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 JARVIS AI - Interface Style LM Studio</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: #2d2d2d;
            padding: 12px 20px;
            border-bottom: 1px solid #404040;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header h1 {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #00ff88;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #00ff88;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-height: calc(100vh - 60px);
        }

        .chat-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #1a1a1a;
        }

        .message {
            margin-bottom: 16px;
            display: flex;
            flex-direction: column;
        }

        .message.user {
            align-items: flex-end;
        }

        .message.assistant {
            align-items: flex-start;
        }

        .message-content {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: #007AFF;
            color: white;
        }

        .message.assistant .message-content {
            background: #2d2d2d;
            color: #ffffff;
            border: 1px solid #404040;
        }

        .message-time {
            font-size: 12px;
            color: #888;
            margin-top: 4px;
            margin-bottom: 4px;
        }

        .input-container {
            background: #2d2d2d;
            border-top: 1px solid #404040;
            padding: 16px 20px;
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        #messageInput {
            width: 100%;
            background: #1a1a1a;
            border: 1px solid #404040;
            border-radius: 8px;
            padding: 12px 16px;
            color: #ffffff;
            font-size: 14px;
            resize: none;
            min-height: 44px;
            max-height: 120px;
            font-family: inherit;
        }

        #messageInput:focus {
            outline: none;
            border-color: #007AFF;
        }

        #messageInput::placeholder {
            color: #888;
        }

        .send-button {
            background: #007AFF;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
            min-width: 80px;
        }

        .send-button:hover {
            background: #0056CC;
        }

        .send-button:disabled {
            background: #404040;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            color: #888;
            font-size: 14px;
            margin-bottom: 16px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: #888;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% { opacity: 0.3; }
            30% { opacity: 1; }
        }

        .welcome-message {
            text-align: center;
            color: #888;
            font-size: 16px;
            margin-top: 40px;
        }

        .welcome-message h2 {
            color: #ffffff;
            margin-bottom: 8px;
        }

        /* Scrollbar styling */
        .chat-container::-webkit-scrollbar {
            width: 6px;
        }

        .chat-container::-webkit-scrollbar-track {
            background: #1a1a1a;
        }

        .chat-container::-webkit-scrollbar-thumb {
            background: #404040;
            border-radius: 3px;
        }

        .chat-container::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 JARVIS AI</h1>
        <div class="status-indicator">
            <div class="status-dot"></div>
            <span>Connecté</span>
        </div>
    </div>

    <div class="main-container">
        <div class="chat-container" id="chatContainer">
            <div class="welcome-message">
                <h2>Bienvenue dans JARVIS AI</h2>
                <p>Votre assistant IA personnel est prêt à vous aider</p>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <span>JARVIS réfléchit</span>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>

        <div class="input-container">
            <div class="input-wrapper">
                <textarea 
                    id="messageInput" 
                    placeholder="Tapez votre message..."
                    rows="1"
                ></textarea>
            </div>
            <button class="send-button" id="sendButton">Envoyer</button>
        </div>
    </div>

    <script>
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const typingIndicator = document.getElementById('typingIndicator');

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        // Send message on Enter (but allow Shift+Enter for new line)
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        sendButton.addEventListener('click', sendMessage);

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Add user message
            addMessage('user', message);
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // Show typing indicator
            showTypingIndicator();

            // Simulate AI response (replace with actual API call)
            setTimeout(() => {
                hideTypingIndicator();
                addMessage('assistant', 'Je suis JARVIS, votre assistant IA. Comment puis-je vous aider aujourd\'hui ?');
            }, 1500);
        }

        function addMessage(sender, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const time = new Date().toLocaleTimeString('fr-FR', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });

            messageDiv.innerHTML = `
                <div class="message-time">${time}</div>
                <div class="message-content">${content}</div>
            `;

            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;

            // Remove welcome message if it exists
            const welcomeMessage = chatContainer.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }
        }

        function showTypingIndicator() {
            typingIndicator.style.display = 'flex';
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function hideTypingIndicator() {
            typingIndicator.style.display = 'none';
        }

        // Focus input on load
        window.addEventListener('load', () => {
            messageInput.focus();
        });
    </script>
</body>
</html>
