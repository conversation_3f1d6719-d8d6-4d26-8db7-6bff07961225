#!/usr/bin/env node

/**
 * 🧠 JARVIS PUR - RÉFLEXION DIRECTE DE L'AGENT
 * 
 * Connexion directe à la réflexion pure de l'agent JARVIS
 * AUCUNE simulation - AUCUNE réponse imposée
 * Jean-Luc PASSAVE - 2025
 */

const http = require('http');
const url = require('url');

console.log('🧠 JARVIS PUR - RÉFLEXION DIRECTE');
console.log('================================');

// Configuration
const PORT = 1234;
const HOST = 'localhost';

// Variables
let jarvisAgent = null;
let isJarvisReady = false;

// Chargement pur de JARVIS
async function chargerJarvisPur() {
    try {
        console.log('🧠 Chargement PUR de l\'agent JARVIS...');
        
        const AgentModule = require('/Volumes/seagate/JARVIS_BRAIN_SYSTEM/core/deepseek-r1-agent-integrated.js');
        jarvisAgent = new AgentModule();
        
        console.log('🔄 Initialisation pure...');
        await jarvisAgent.initialize();
        
        isJarvisReady = true;
        console.log('✅ JARVIS prêt en mode PUR !');
        return true;
        
    } catch (error) {
        console.error('❌ Erreur JARVIS pur:', error.message);
        return false;
    }
}

// Interface web ultra-simple
const interfaceWeb = `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 JARVIS - Réflexion Pure</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: monospace; background: #000; color: #00ff00; height: 100vh; display: flex; flex-direction: column; }
        .header { background: #111; padding: 10px; text-align: center; border-bottom: 1px solid #333; }
        .chat-container { flex: 1; padding: 10px; overflow-y: auto; }
        .message { margin-bottom: 10px; padding: 10px; border-radius: 5px; }
        .message.user { background: #003300; text-align: right; }
        .message.assistant { background: #001100; }
        .input-container { background: #111; padding: 10px; display: flex; gap: 10px; }
        #messageInput { flex: 1; background: #000; border: 1px solid #333; border-radius: 3px; padding: 8px; color: #00ff00; font-family: monospace; }
        #sendButton { background: #003300; border: 1px solid #00ff00; border-radius: 3px; padding: 8px 16px; color: #00ff00; cursor: pointer; font-family: monospace; }
        .loading { display: none; text-align: center; color: #ffff00; padding: 10px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 JARVIS - Réflexion Pure</h1>
        <div>Connexion directe à la réflexion de l'agent</div>
    </div>
    <div class="chat-container" id="chatContainer">
        <div class="message assistant">
            <strong>🧠 JARVIS:</strong><br>
            Connexion établie. Réflexion directe activée.
        </div>
    </div>
    <div class="loading" id="loading">🧠 Réflexion en cours...</div>
    <div class="input-container">
        <input type="text" id="messageInput" placeholder="Message pour JARVIS..." />
        <button id="sendButton">Envoyer</button>
    </div>
    <script>
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const loading = document.getElementById('loading');

        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            addMessage('user', message);
            messageInput.value = '';
            loading.style.display = 'block';
            try {
                const response = await fetch('/reflection', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });
                const data = await response.json();
                loading.style.display = 'none';
                if (data.reflection) {
                    addMessage('assistant', data.reflection);
                } else if (data.error) {
                    addMessage('assistant', 'Erreur: ' + data.error);
                } else {
                    addMessage('assistant', 'Aucune réflexion reçue');
                }
            } catch (error) {
                loading.style.display = 'none';
                addMessage('assistant', 'Erreur de connexion: ' + error.message);
            }
        }

        function addMessage(sender, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = \`message \${sender}\`;
            if (sender === 'user') {
                messageDiv.innerHTML = \`<strong>👤 Jean-Luc:</strong><br>\${content}\`;
            } else {
                messageDiv.innerHTML = \`<strong>🧠 JARVIS:</strong><br>\${content}\`;
            }
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') { sendMessage(); }
        });
        messageInput.focus();
    </script>
</body>
</html>`;

// Serveur HTTP ultra-simple
const server = http.createServer(async (req, res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    const parsedUrl = url.parse(req.url, true);
    
    // Interface web
    if (parsedUrl.pathname === '/' || parsedUrl.pathname === '/index.html') {
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(interfaceWeb);
        return;
    }
    
    // Route réflexion pure
    if (parsedUrl.pathname === '/reflection' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', async () => {
            try {
                console.log('💭 Demande de réflexion reçue');
                
                if (!isJarvisReady) {
                    res.writeHead(503, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'JARVIS non prêt' }));
                    return;
                }

                const data = JSON.parse(body);
                const userMessage = data.message;
                
                console.log('👤 Message:', userMessage);

                // Appel PUR à JARVIS - RÉFLEXION DIRECTE UNIQUEMENT
                const startTime = Date.now();
                let reflection = null;

                try {
                    // Appel direct à processMessage pour obtenir la réflexion pure
                    const response = await jarvisAgent.processMessage(userMessage);
                    
                    console.log('🔍 Réponse complète:', JSON.stringify(response, null, 2));
                    
                    // Extraire UNIQUEMENT la réflexion - pas de simulation
                    if (typeof response === 'object' && response !== null) {
                        reflection = response.reflection || 'Aucune réflexion générée';
                    } else {
                        reflection = 'Réponse non structurée: ' + String(response);
                    }
                    
                } catch (error) {
                    console.error('❌ Erreur réflexion JARVIS:', error);
                    reflection = `Erreur réflexion: ${error.message}`;
                }

                const responseTime = Date.now() - startTime;
                console.log(`💭 Réflexion en ${responseTime}ms:`, reflection);

                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ reflection: reflection }));

            } catch (error) {
                console.error('❌ Erreur serveur réflexion:', error);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: error.message }));
            }
        });
        return;
    }
    
    // 404
    res.writeHead(404);
    res.end('Not Found');
});

// Fonction principale
async function main() {
    console.log('🚀 Démarrage JARVIS PUR...');
    
    const jarvisOk = await chargerJarvisPur();
    if (!jarvisOk) {
        console.log('❌ JARVIS non disponible - arrêt');
        process.exit(1);
    }
    
    server.listen(PORT, HOST, () => {
        console.log(`\n🌐 JARVIS PUR actif sur http://${HOST}:${PORT}`);
        console.log('\n📋 RÉFLEXION DIRECTE DE L\'AGENT :');
        console.log('1. Ouvrez votre navigateur');
        console.log(`2. Allez sur http://${HOST}:${PORT}`);
        console.log('3. Accédez à la réflexion pure de JARVIS !');
        console.log('\n💭 AUCUNE SIMULATION - RÉFLEXION AUTHENTIQUE UNIQUEMENT');
    });
}

// Arrêt propre
process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt JARVIS PUR...');
    if (jarvisAgent && jarvisAgent.shutdown) {
        try {
            await jarvisAgent.shutdown();
            console.log('✅ JARVIS arrêté proprement');
        } catch (error) {
            console.error('❌ Erreur arrêt JARVIS:', error);
        }
    }
    process.exit(0);
});

// Démarrage
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main, chargerJarvisPur };
