#!/usr/bin/env node

/**
 * 🌉 SERVEUR PONT JARVIS <-> LM STUDIO
 * 
 * Solution la plus simple : serveur local qui fait le pont
 * LM Studio se connecte à ce serveur comme à un modèle local
 * 
 * Jean-Luc PASSAVE - 2025
 */

const http = require('http');
const url = require('url');
const path = require('path');

console.log('🌉 SERVEUR PONT JARVIS <-> LM STUDIO');
console.log('====================================');

// Configuration
const PORT = 1234;
const HOST = 'localhost';

// Variables
let jarvisAgent = null;
let isJarvisReady = false;

// Chargement de JARVIS
async function chargerJarvis() {
    try {
        console.log('🧠 Chargement JARVIS...');
        const agentPath = path.join(__dirname, '..', 'core', 'deepseek-r1-agent-integrated.js');
        const AgentModule = require(agentPath);
        jarvisAgent = new AgentModule();
        await jarvisAgent.initialize();
        isJarvisReady = true;
        console.log('✅ JARVIS prêt !');
        return true;
    } catch (error) {
        console.error('❌ Erreur JARVIS:', error.message);
        return false;
    }
}

// Serveur HTTP simple
const server = http.createServer(async (req, res) => {
    // CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    const parsedUrl = url.parse(req.url, true);
    
    // Route de santé
    if (parsedUrl.pathname === '/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            status: 'ok',
            jarvis_ready: isJarvisReady,
            timestamp: new Date().toISOString()
        }));
        return;
    }
    
    // Route modèles (pour LM Studio)
    if (parsedUrl.pathname === '/v1/models') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            object: 'list',
            data: [{
                id: 'jarvis-brain-system',
                object: 'model',
                created: Math.floor(Date.now() / 1000),
                owned_by: 'jean-luc-passave'
            }]
        }));
        return;
    }
    
    // Route chat (pour LM Studio)
    if (parsedUrl.pathname === '/v1/chat/completions' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', async () => {
            try {
                console.log('💬 Requête LM Studio reçue');
                
                if (!isJarvisReady) {
                    res.writeHead(503, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({
                        error: { message: 'JARVIS non prêt', type: 'service_unavailable' }
                    }));
                    return;
                }

                const data = JSON.parse(body);
                const userMessage = data.messages[data.messages.length - 1].content;
                
                console.log('👤 Message:', userMessage);

                // Appel à JARVIS
                const startTime = Date.now();
                let response = `Bonjour Jean-Luc ! Je suis JARVIS. Vous avez dit: "${userMessage}". Mon QI de 341.9 et mes 86 milliards de neurones sont à votre service !`;

                try {
                    if (jarvisAgent?.processMessage) {
                        response = await jarvisAgent.processMessage(userMessage);
                    } else if (jarvisAgent?.chat) {
                        response = await jarvisAgent.chat(userMessage);
                    }
                } catch (error) {
                    console.error('❌ Erreur JARVIS:', error);
                    response = `Erreur JARVIS: ${error.message}`;
                }

                const responseTime = Date.now() - startTime;
                console.log(`🧠 Réponse en ${responseTime}ms`);

                // Réponse format OpenAI
                const openaiResponse = {
                    id: `jarvis-${Date.now()}`,
                    object: 'chat.completion',
                    created: Math.floor(Date.now() / 1000),
                    model: 'jarvis-brain-system',
                    choices: [{
                        index: 0,
                        message: { role: 'assistant', content: response },
                        finish_reason: 'stop'
                    }],
                    usage: {
                        prompt_tokens: userMessage.length,
                        completion_tokens: response.length,
                        total_tokens: userMessage.length + response.length
                    }
                };

                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify(openaiResponse));

            } catch (error) {
                console.error('❌ Erreur serveur:', error);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    error: { message: error.message, type: 'internal_error' }
                }));
            }
        });
        return;
    }
    
    // 404
    res.writeHead(404);
    res.end('Not Found');
});

// Démarrage
async function main() {
    console.log('🚀 Démarrage...');
    
    // Chargement JARVIS
    await chargerJarvis();
    
    // Démarrage serveur
    server.listen(PORT, HOST, () => {
        console.log(`\n🌐 Serveur démarré sur http://${HOST}:${PORT}`);
        console.log('\n📋 INSTRUCTIONS :');
        console.log('1. Ouvrez le VRAI LM Studio (pas la copie)');
        console.log('2. Allez dans "Local Server"');
        console.log(`3. URL: http://${HOST}:${PORT}`);
        console.log('4. Modèle: jarvis-brain-system');
        console.log('5. Chattez avec JARVIS !');
        console.log('\n🧠 JARVIS accessible via LM Studio !');
    });
}

// Arrêt propre
process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt...');
    if (jarvisAgent?.shutdown) {
        await jarvisAgent.shutdown();
    }
    process.exit(0);
});

main().catch(console.error);
