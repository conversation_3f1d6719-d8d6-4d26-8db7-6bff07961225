#!/usr/bin/env node

/**
 * 🌉 PONT JARVIS <-> LM STUDIO
 * 
 * Serveur qui fait le pont entre LM Studio et l'agent JARVIS
 * LM Studio se connecte à ce serveur comme à un modèle local
 * 
 * SOLUTION LA PLUS SIMPLE ET RAPIDE
 * Jean-Luc PASSAVE - 2025
 */

const express = require('express');
const cors = require('cors');
const path = require('path');

// Configuration
const PORT = 1234; // Port standard LM Studio
const HOST = 'localhost';

console.log('🌉 PONT JARVIS <-> LM STUDIO');
console.log('============================');

// Chargement de l'agent JARVIS
let jarvisAgent = null;
let isJarvisReady = false;

async function initializeJarvis() {
    try {
        console.log('🧠 Chargement de l\'agent JARVIS...');
        
        // Chargement de l'agent depuis le dossier core
        const agentPath = path.join(__dirname, '..', '..', 'core', 'deepseek-r1-agent-integrated.js');
        console.log('📂 Chemin agent:', agentPath);
        
        const AgentModule = require(agentPath);
        jarvisAgent = new AgentModule();
        
        console.log('🔄 Initialisation de l\'agent...');
        await jarvisAgent.initialize();
        
        isJarvisReady = true;
        console.log('✅ Agent JARVIS prêt !');
        
        return true;
        
    } catch (error) {
        console.error('❌ Erreur chargement JARVIS:', error);
        return false;
    }
}

// Création du serveur Express
const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Route de santé
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        jarvis_ready: isJarvisReady,
        timestamp: new Date().toISOString()
    });
});

// Route compatible OpenAI pour LM Studio
app.post('/v1/chat/completions', async (req, res) => {
    try {
        console.log('💬 Requête LM Studio reçue');
        
        if (!isJarvisReady) {
            return res.status(503).json({
                error: {
                    message: 'JARVIS n\'est pas encore prêt',
                    type: 'service_unavailable'
                }
            });
        }

        const { messages } = req.body;
        const lastMessage = messages[messages.length - 1];
        const userMessage = lastMessage.content;

        console.log('🧠 Message utilisateur:', userMessage);

        // Appel direct à JARVIS
        const startTime = Date.now();
        let response;

        if (typeof jarvisAgent.processMessage === 'function') {
            response = await jarvisAgent.processMessage(userMessage);
        } else if (typeof jarvisAgent.chat === 'function') {
            response = await jarvisAgent.chat(userMessage);
        } else {
            response = `Bonjour ! Je suis JARVIS, votre cerveau artificiel. Vous avez dit: "${userMessage}". Comment puis-je vous aider ?`;
        }

        const responseTime = Date.now() - startTime;
        console.log(`✅ Réponse JARVIS en ${responseTime}ms`);

        // Format de réponse compatible OpenAI
        const openaiResponse = {
            id: `jarvis-${Date.now()}`,
            object: 'chat.completion',
            created: Math.floor(Date.now() / 1000),
            model: 'jarvis-brain-system',
            choices: [{
                index: 0,
                message: {
                    role: 'assistant',
                    content: response
                },
                finish_reason: 'stop'
            }],
            usage: {
                prompt_tokens: userMessage.length,
                completion_tokens: response.length,
                total_tokens: userMessage.length + response.length
            }
        };

        res.json(openaiResponse);

    } catch (error) {
        console.error('❌ Erreur traitement:', error);
        res.status(500).json({
            error: {
                message: 'Erreur interne JARVIS: ' + error.message,
                type: 'internal_error'
            }
        });
    }
});

// Route pour lister les modèles (pour LM Studio)
app.get('/v1/models', (req, res) => {
    res.json({
        object: 'list',
        data: [{
            id: 'jarvis-brain-system',
            object: 'model',
            created: Math.floor(Date.now() / 1000),
            owned_by: 'jean-luc-passave',
            permission: [],
            root: 'jarvis-brain-system',
            parent: null
        }]
    });
});

// Démarrage du serveur
async function startServer() {
    // Initialisation de JARVIS
    const jarvisOk = await initializeJarvis();
    
    if (!jarvisOk) {
        console.log('⚠️  JARVIS non disponible - serveur en mode dégradé');
    }

    // Démarrage du serveur
    app.listen(PORT, HOST, () => {
        console.log(`🚀 Serveur démarré sur http://${HOST}:${PORT}`);
        console.log('');
        console.log('📋 INSTRUCTIONS POUR LM STUDIO :');
        console.log('1. Ouvrez LM Studio');
        console.log('2. Allez dans "Local Server"');
        console.log(`3. Configurez l'URL: http://${HOST}:${PORT}`);
        console.log('4. Sélectionnez le modèle "jarvis-brain-system"');
        console.log('5. Commencez à chatter avec JARVIS !');
        console.log('');
        console.log('🧠 JARVIS est maintenant accessible via LM Studio !');
    });
}

// Gestion des erreurs
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non capturée:', error);
});

process.on('unhandledRejection', (reason) => {
    console.error('❌ Promesse rejetée:', reason);
});

// Gestion de l'arrêt propre
process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt du serveur...');
    
    if (jarvisAgent && typeof jarvisAgent.shutdown === 'function') {
        try {
            await jarvisAgent.shutdown();
            console.log('✅ JARVIS arrêté proprement');
        } catch (error) {
            console.error('❌ Erreur arrêt JARVIS:', error);
        }
    }
    
    process.exit(0);
});

// Démarrage
startServer();
