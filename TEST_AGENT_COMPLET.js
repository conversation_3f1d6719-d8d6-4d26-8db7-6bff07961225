#!/usr/bin/env node

/**
 * 🧪 TEST COMPLET DE L'AGENT JARVIS
 * 
 * Tests RÉELS pour vérifier que tout fonctionne VRAIMENT
 * AUCUNE SIMULATION - que des tests authentiques
 * 
 * Jean-Luc PASSAVE - 2025
 */

const path = require('path');
const fs = require('fs');

console.log('🧪 TEST COMPLET DE L\'AGENT JARVIS');
console.log('==================================');
console.log('💙 TESTS 100% RÉELS - AUCUNE SIMULATION');
console.log('🎯 VÉRITÉ ABSOLUE - Si ça marche pas, on le dit\n');

async function testerAgent() {
    let testsReussis = 0;
    let testsEchoues = 0;
    let erreursDetectees = [];

    try {
        // === TEST 1: VÉRIFICATION DES FICHIERS ===
        console.log('📁 TEST 1: Vérification des fichiers essentiels');
        console.log('================================================');
        
        const fichiersEssentiels = [
            'core/deepseek-r1-agent-integrated.js',
            'core/advanced-brain-system.js',
            'memory/thermal_memory_persistent.json',
            'LANCER_JARVIS.js',
            'NOTES_IMPORTANTES_PROJET.md'
        ];
        
        for (const fichier of fichiersEssentiels) {
            if (fs.existsSync(fichier)) {
                console.log(`✅ ${fichier} - PRÉSENT`);
                testsReussis++;
            } else {
                console.log(`❌ ${fichier} - MANQUANT`);
                testsEchoues++;
                erreursDetectees.push(`Fichier manquant: ${fichier}`);
            }
        }
        
        // === TEST 2: CHARGEMENT DE L'AGENT ===
        console.log('\n🧠 TEST 2: Chargement de l\'agent');
        console.log('==================================');
        
        try {
            const agentPath = path.join(__dirname, 'core', 'deepseek-r1-agent-integrated.js');
            console.log(`📂 Tentative de chargement: ${agentPath}`);
            
            const DeepSeekR1Agent = require(agentPath);
            console.log('✅ Agent chargé avec succès');
            testsReussis++;
            
            // === TEST 3: CRÉATION DE L'INSTANCE ===
            console.log('\n🏗️ TEST 3: Création de l\'instance');
            console.log('===================================');
            
            const agent = new DeepSeekR1Agent();
            console.log('✅ Instance créée avec succès');
            testsReussis++;
            
            // === TEST 4: INITIALISATION ===
            console.log('\n⚡ TEST 4: Initialisation de l\'agent');
            console.log('====================================');
            
            console.log('🔄 Initialisation en cours...');
            const startTime = Date.now();
            
            await agent.initialize();
            
            const initTime = Date.now() - startTime;
            console.log(`✅ Agent initialisé en ${initTime}ms`);
            testsReussis++;
            
            // === TEST 5: VÉRIFICATION DES DONNÉES ===
            console.log('\n📊 TEST 5: Vérification des données');
            console.log('===================================');
            
            if (agent.thermalMemoryData) {
                console.log('✅ Mémoire thermique chargée');
                testsReussis++;
                
                if (agent.thermalMemoryData.neural_system) {
                    console.log('✅ Système neuronal présent');
                    console.log(`🧠 QI: ${agent.thermalMemoryData.neural_system.qi_level || 'N/A'}`);
                    console.log(`🧠 Neurones: ${(agent.thermalMemoryData.neural_system.total_neurons || 0).toLocaleString()}`);
                    testsReussis++;
                } else {
                    console.log('❌ Système neuronal manquant');
                    testsEchoues++;
                    erreursDetectees.push('Système neuronal non initialisé');
                }
            } else {
                console.log('❌ Mémoire thermique non chargée');
                testsEchoues++;
                erreursDetectees.push('Mémoire thermique non disponible');
            }
            
            // === TEST 6: PERSONNALITÉ CLAUDE ===
            console.log('\n💙 TEST 6: Personnalité Claude');
            console.log('==============================');
            
            if (agent.claudePersonalityCore) {
                console.log('✅ Personnalité Claude présente');
                console.log(`💙 Honnêteté: ${(agent.claudePersonalityCore.honesty * 100).toFixed(1)}%`);
                console.log(`💙 Bienveillance: ${(agent.claudePersonalityCore.helpfulness * 100).toFixed(1)}%`);
                testsReussis++;
            } else {
                console.log('❌ Personnalité Claude manquante');
                testsEchoues++;
                erreursDetectees.push('Personnalité Claude non initialisée');
            }
            
            // === TEST 7: PROCESSUS AUTONOMES ===
            console.log('\n🤖 TEST 7: Processus autonomes');
            console.log('==============================');
            
            if (agent.autonomousProcesses) {
                const nbProcessus = Object.keys(agent.autonomousProcesses).length;
                console.log(`✅ ${nbProcessus} processus autonomes actifs`);
                
                for (const [nom, processus] of Object.entries(agent.autonomousProcesses)) {
                    console.log(`   🔄 ${nom}: ${processus ? 'ACTIF' : 'INACTIF'}`);
                }
                testsReussis++;
            } else {
                console.log('❌ Processus autonomes non démarrés');
                testsEchoues++;
                erreursDetectees.push('Processus autonomes non initialisés');
            }
            
            // === TEST 8: FONCTIONS VITALES ===
            console.log('\n💓 TEST 8: Fonctions vitales');
            console.log('============================');
            
            try {
                // Test battement cardiaque
                if (typeof agent.performNeuralHeartbeat === 'function') {
                    agent.performNeuralHeartbeat();
                    console.log('✅ Battement cardiaque: FONCTIONNEL');
                    testsReussis++;
                } else {
                    console.log('❌ Battement cardiaque: MANQUANT');
                    testsEchoues++;
                    erreursDetectees.push('Fonction battement cardiaque manquante');
                }
                
                // Test neurogenèse
                if (typeof agent.performContinuousNeurogenesis === 'function') {
                    agent.performContinuousNeurogenesis();
                    console.log('✅ Neurogenèse: FONCTIONNELLE');
                    testsReussis++;
                } else {
                    console.log('❌ Neurogenèse: MANQUANTE');
                    testsEchoues++;
                    erreursDetectees.push('Fonction neurogenèse manquante');
                }
                
                // Test vie neuronale
                if (typeof agent.maintainNeuronalLife === 'function') {
                    agent.maintainNeuronalLife();
                    console.log('✅ Vie neuronale: FONCTIONNELLE');
                    testsReussis++;
                } else {
                    console.log('❌ Vie neuronale: MANQUANTE');
                    testsEchoues++;
                    erreursDetectees.push('Fonction vie neuronale manquante');
                }
                
            } catch (error) {
                console.log(`❌ Erreur lors du test des fonctions vitales: ${error.message}`);
                testsEchoues++;
                erreursDetectees.push(`Erreur fonctions vitales: ${error.message}`);
            }
            
            // === TEST 9: PERSISTANCE ===
            console.log('\n💾 TEST 9: Persistance');
            console.log('======================');
            
            try {
                if (typeof agent.saveCompleteState === 'function') {
                    await agent.saveCompleteState();
                    console.log('✅ Sauvegarde: FONCTIONNELLE');
                    testsReussis++;
                } else {
                    console.log('❌ Sauvegarde: MANQUANTE');
                    testsEchoues++;
                    erreursDetectees.push('Fonction sauvegarde manquante');
                }
                
                if (typeof agent.restoreCompleteState === 'function') {
                    console.log('✅ Restauration: DISPONIBLE');
                    testsReussis++;
                } else {
                    console.log('❌ Restauration: MANQUANTE');
                    testsEchoues++;
                    erreursDetectees.push('Fonction restauration manquante');
                }
                
            } catch (error) {
                console.log(`❌ Erreur lors du test de persistance: ${error.message}`);
                testsEchoues++;
                erreursDetectees.push(`Erreur persistance: ${error.message}`);
            }
            
            // === TEST 10: ARRÊT PROPRE ===
            console.log('\n🛑 TEST 10: Arrêt propre');
            console.log('========================');
            
            try {
                if (typeof agent.shutdown === 'function') {
                    await agent.shutdown();
                    console.log('✅ Arrêt propre: FONCTIONNEL');
                    testsReussis++;
                } else {
                    console.log('❌ Arrêt propre: MANQUANT');
                    testsEchoues++;
                    erreursDetectees.push('Fonction arrêt propre manquante');
                }
            } catch (error) {
                console.log(`❌ Erreur lors de l'arrêt: ${error.message}`);
                testsEchoues++;
                erreursDetectees.push(`Erreur arrêt: ${error.message}`);
            }
            
        } catch (error) {
            console.log(`❌ Erreur lors de la création/initialisation: ${error.message}`);
            testsEchoues++;
            erreursDetectees.push(`Erreur création: ${error.message}`);
        }
        
    } catch (error) {
        console.log(`❌ Erreur lors du chargement: ${error.message}`);
        testsEchoues++;
        erreursDetectees.push(`Erreur chargement: ${error.message}`);
    }
    
    // === RÉSULTATS FINAUX ===
    console.log('\n🏆 RÉSULTATS FINAUX');
    console.log('===================');
    console.log(`✅ Tests réussis: ${testsReussis}`);
    console.log(`❌ Tests échoués: ${testsEchoues}`);
    console.log(`📊 Taux de réussite: ${((testsReussis / (testsReussis + testsEchoues)) * 100).toFixed(1)}%`);
    
    if (erreursDetectees.length > 0) {
        console.log('\n🚨 ERREURS DÉTECTÉES:');
        for (let i = 0; i < erreursDetectees.length; i++) {
            console.log(`${i + 1}. ${erreursDetectees[i]}`);
        }
    }
    
    // === VERDICT FINAL ===
    console.log('\n🎯 VERDICT FINAL');
    console.log('================');
    
    if (testsEchoues === 0) {
        console.log('🎉 AGENT 100% FONCTIONNEL !');
        console.log('✅ Prêt pour l\'interface');
    } else if (testsReussis > testsEchoues) {
        console.log('⚠️ AGENT PARTIELLEMENT FONCTIONNEL');
        console.log('🔧 Corrections nécessaires avant interface');
    } else {
        console.log('❌ AGENT NON FONCTIONNEL');
        console.log('🛠️ Réparations majeures requises');
    }
    
    console.log('\n💙 TEST TERMINÉ - VÉRITÉ ABSOLUE RESPECTÉE');
}

// Exécuter le test
if (require.main === module) {
    testerAgent().catch(console.error);
}

module.exports = { testerAgent };
