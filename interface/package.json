{"name": "jarvis-studio", "version": "1.0.0", "description": "Interface LM Studio avec JARVIS Brain System intégré directement", "main": "jarvis-studio-electron.js", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"start": "electron .", "dev": "NODE_ENV=development electron .", "build": "electron-builder", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never"}, "keywords": ["jarvis", "ai", "brain", "lm-studio", "electron", "neural-network"], "dependencies": {"electron": "^25.0.0"}, "devDependencies": {"electron-builder": "^24.0.0"}, "build": {"appId": "com.jeanluc.jarvis-studio", "productName": "JARVIS Studio", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist", "!*.log"], "mac": {"category": "public.app-category.productivity", "icon": "assets/jarvis-icon.icns", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"icon": "assets/jarvis-icon.ico", "target": [{"target": "nsis", "arch": ["x64"]}]}, "linux": {"icon": "assets/jarvis-icon.png", "target": [{"target": "AppImage", "arch": ["x64"]}]}}, "repository": {"type": "git", "url": "https://github.com/jeanluc/jarvis-studio.git"}, "bugs": {"url": "https://github.com/jeanluc/jarvis-studio/issues"}, "homepage": "https://github.com/jeanluc/jarvis-studio#readme"}