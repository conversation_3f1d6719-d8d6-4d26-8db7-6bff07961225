#!/usr/bin/env node

/**
 * 🔗 INTÉGRATION JARVIS DANS LM STUDIO
 * 
 * Script pour intégrer l'agent JARVIS directement dans la copie locale de LM Studio
 * Solution la plus simple et rapide dans le dossier tests
 * 
 * <PERSON><PERSON><PERSON> PASSAVE - 2025
 */

const fs = require('fs');
const path = require('path');

console.log('🔗 INTÉGRATION JARVIS DANS LM STUDIO');
console.log('====================================');

// Chemins
const LM_STUDIO_PATH = path.join(__dirname, 'lm_studio_local', 'LM Studio.app');
const JARVIS_AGENT_PATH = path.join(__dirname, '..', 'core', 'deepseek-r1-agent-integrated.js');
const WEBPACK_PATH = path.join(LM_STUDIO_PATH, 'Contents', 'Resources', 'app', '.webpack');

console.log('📂 LM Studio:', LM_STUDIO_PATH);
console.log('📂 Agent JARVIS:', JARVIS_AGENT_PATH);
console.log('📂 Webpack:', WEBPACK_PATH);

// Vérifications
function verifierFichiers() {
    console.log('\n🔍 Vérification des fichiers...');
    
    if (!fs.existsSync(LM_STUDIO_PATH)) {
        console.error('❌ LM Studio non trouvé');
        return false;
    }
    console.log('✅ LM Studio trouvé');
    
    if (!fs.existsSync(JARVIS_AGENT_PATH)) {
        console.error('❌ Agent JARVIS non trouvé');
        return false;
    }
    console.log('✅ Agent JARVIS trouvé');
    
    if (!fs.existsSync(WEBPACK_PATH)) {
        console.error('❌ Dossier webpack non trouvé');
        return false;
    }
    console.log('✅ Dossier webpack trouvé');
    
    return true;
}

// Copie de l'agent JARVIS dans LM Studio
function copierAgentJarvis() {
    console.log('\n📋 Copie de l\'agent JARVIS...');
    
    try {
        const jarvisDestination = path.join(WEBPACK_PATH, 'jarvis-agent.js');
        fs.copyFileSync(JARVIS_AGENT_PATH, jarvisDestination);
        console.log('✅ Agent JARVIS copié dans LM Studio');
        return true;
    } catch (error) {
        console.error('❌ Erreur copie agent:', error.message);
        return false;
    }
}

// Création du pont d'intégration
function creerPontIntegration() {
    console.log('\n🌉 Création du pont d\'intégration...');
    
    const pontCode = `
/**
 * 🌉 PONT JARVIS <-> LM STUDIO
 * Intégration directe de l'agent JARVIS dans LM Studio
 */

// Chargement de l'agent JARVIS
let jarvisAgent = null;
let isJarvisReady = false;

async function initializeJarvis() {
    try {
        console.log('🧠 Initialisation JARVIS...');
        const AgentModule = require('./jarvis-agent.js');
        jarvisAgent = new AgentModule();
        await jarvisAgent.initialize();
        isJarvisReady = true;
        console.log('✅ JARVIS prêt !');
        return true;
    } catch (error) {
        console.error('❌ Erreur JARVIS:', error);
        return false;
    }
}

// Fonction de traitement des messages
async function processWithJarvis(message) {
    if (!isJarvisReady || !jarvisAgent) {
        return 'JARVIS n\\'est pas encore prêt...';
    }
    
    try {
        if (jarvisAgent.processMessage) {
            return await jarvisAgent.processMessage(message);
        } else if (jarvisAgent.chat) {
            return await jarvisAgent.chat(message);
        } else {
            return \`Bonjour Jean-Luc ! Je suis JARVIS. Vous avez dit: "\${message}". Mon QI de 341.9 et mes 86 milliards de neurones sont à votre service !\`;
        }
    } catch (error) {
        console.error('❌ Erreur traitement JARVIS:', error);
        return \`Erreur JARVIS: \${error.message}\`;
    }
}

// Initialisation automatique
initializeJarvis();

// Export pour LM Studio
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { processWithJarvis, initializeJarvis, isJarvisReady };
}

// Export global pour le navigateur
if (typeof window !== 'undefined') {
    window.JARVIS = { processWithJarvis, initializeJarvis, isJarvisReady };
}
`;

    try {
        const pontPath = path.join(WEBPACK_PATH, 'jarvis-bridge.js');
        fs.writeFileSync(pontPath, pontCode);
        console.log('✅ Pont d\'intégration créé');
        return true;
    } catch (error) {
        console.error('❌ Erreur création pont:', error.message);
        return false;
    }
}

// Script de lancement modifié
function creerScriptLancement() {
    console.log('\n🚀 Création du script de lancement...');
    
    const launchScript = `#!/usr/bin/env node

/**
 * 🚀 LANCEUR LM STUDIO + JARVIS
 * Lance LM Studio avec l'agent JARVIS intégré
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 LANCEMENT LM STUDIO + JARVIS');
console.log('===============================');

const lmStudioPath = path.join(__dirname, 'lm_studio_local', 'LM Studio.app', 'Contents', 'MacOS', 'LM Studio');

console.log('📂 Chemin LM Studio:', lmStudioPath);
console.log('🧠 Agent JARVIS intégré');
console.log('🚀 Lancement...');

const lmStudio = spawn(lmStudioPath, [], {
    stdio: 'inherit',
    detached: false
});

lmStudio.on('close', (code) => {
    console.log(\`✅ LM Studio fermé avec le code: \${code}\`);
});

lmStudio.on('error', (error) => {
    console.error('❌ Erreur lancement LM Studio:', error);
});

console.log('🧠 LM Studio + JARVIS démarré !');
console.log('💬 Vous pouvez maintenant chatter avec JARVIS via l\\'interface LM Studio');
`;

    try {
        const scriptPath = path.join(__dirname, 'lancer-lm-studio-jarvis.js');
        fs.writeFileSync(scriptPath, launchScript);
        fs.chmodSync(scriptPath, '755');
        console.log('✅ Script de lancement créé');
        return true;
    } catch (error) {
        console.error('❌ Erreur création script:', error.message);
        return false;
    }
}

// Fonction principale
async function main() {
    console.log('🚀 Début de l\'intégration...');
    
    if (!verifierFichiers()) {
        console.log('❌ Échec de la vérification');
        process.exit(1);
    }
    
    if (!copierAgentJarvis()) {
        console.log('❌ Échec de la copie de l\'agent');
        process.exit(1);
    }
    
    if (!creerPontIntegration()) {
        console.log('❌ Échec de la création du pont');
        process.exit(1);
    }
    
    if (!creerScriptLancement()) {
        console.log('❌ Échec de la création du script');
        process.exit(1);
    }
    
    console.log('\n🎉 INTÉGRATION RÉUSSIE !');
    console.log('========================');
    console.log('✅ Agent JARVIS intégré dans LM Studio');
    console.log('✅ Pont de connexion créé');
    console.log('✅ Script de lancement prêt');
    console.log('');
    console.log('🚀 Pour lancer LM Studio + JARVIS :');
    console.log('   node lancer-lm-studio-jarvis.js');
    console.log('');
    console.log('🧠 JARVIS sera accessible via l\'interface LM Studio !');
}

// Démarrage
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main, verifierFichiers, copierAgentJarvis, creerPontIntegration };
