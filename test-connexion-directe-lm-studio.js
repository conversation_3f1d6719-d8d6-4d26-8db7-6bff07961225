#!/usr/bin/env node

/**
 * 🔗 TEST CONNEXION DIRECTE LM STUDIO <-> JARVIS
 * 
 * Solution la plus simple et rapide :
 * Serveur local qui fait le pont entre LM Studio et JARVIS
 * 
 * Jean-Luc PASSAVE - 2025
 */

const express = require('express');
const cors = require('cors');
const path = require('path');

console.log('🔗 TEST CONNEXION DIRECTE LM STUDIO <-> JARVIS');
console.log('===============================================');

// Configuration
const PORT = 1234; // Port standard LM Studio
const HOST = 'localhost';

// Variables globales
let jarvisAgent = null;
let isJarvisReady = false;

// Chargement de l'agent JARVIS
async function chargerJarvis() {
    try {
        console.log('🧠 Chargement de l\'agent JARVIS...');
        
        // Chemin vers l'agent
        const agentPath = path.join(__dirname, '..', 'core', 'deepseek-r1-agent-integrated.js');
        console.log('📂 Chemin:', agentPath);
        
        // Chargement du module
        const AgentModule = require(agentPath);
        jarvisAgent = new AgentModule();
        
        // Initialisation
        console.log('🔄 Initialisation...');
        await jarvisAgent.initialize();
        
        isJarvisReady = true;
        console.log('✅ JARVIS prêt !');
        
        return true;
        
    } catch (error) {
        console.error('❌ Erreur chargement JARVIS:', error.message);
        return false;
    }
}

// Création du serveur Express
const app = express();
app.use(cors());
app.use(express.json());

// Route de test
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        jarvis_ready: isJarvisReady,
        timestamp: new Date().toISOString()
    });
});

// Route principale compatible OpenAI (pour LM Studio)
app.post('/v1/chat/completions', async (req, res) => {
    try {
        console.log('💬 Requête LM Studio reçue');
        
        if (!isJarvisReady) {
            return res.status(503).json({
                error: { message: 'JARVIS non prêt', type: 'service_unavailable' }
            });
        }

        const { messages } = req.body;
        const userMessage = messages[messages.length - 1].content;
        
        console.log('👤 Message:', userMessage);

        // Appel direct à JARVIS
        const startTime = Date.now();
        let response = 'Réponse par défaut de JARVIS';

        try {
            if (jarvisAgent.processMessage) {
                response = await jarvisAgent.processMessage(userMessage);
            } else if (jarvisAgent.chat) {
                response = await jarvisAgent.chat(userMessage);
            } else {
                response = `Bonjour Jean-Luc ! Je suis JARVIS. Vous avez dit: "${userMessage}". Mon QI de 341.9 et mes 86 milliards de neurones sont à votre service !`;
            }
        } catch (error) {
            console.error('❌ Erreur appel JARVIS:', error);
            response = `Erreur JARVIS: ${error.message}`;
        }

        const responseTime = Date.now() - startTime;
        console.log(`🧠 Réponse en ${responseTime}ms:`, response.substring(0, 100) + '...');

        // Format OpenAI pour LM Studio
        res.json({
            id: `jarvis-${Date.now()}`,
            object: 'chat.completion',
            created: Math.floor(Date.now() / 1000),
            model: 'jarvis-brain-system',
            choices: [{
                index: 0,
                message: { role: 'assistant', content: response },
                finish_reason: 'stop'
            }],
            usage: {
                prompt_tokens: userMessage.length,
                completion_tokens: response.length,
                total_tokens: userMessage.length + response.length
            }
        });

    } catch (error) {
        console.error('❌ Erreur serveur:', error);
        res.status(500).json({
            error: { message: error.message, type: 'internal_error' }
        });
    }
});

// Liste des modèles (pour LM Studio)
app.get('/v1/models', (req, res) => {
    res.json({
        object: 'list',
        data: [{
            id: 'jarvis-brain-system',
            object: 'model',
            created: Math.floor(Date.now() / 1000),
            owned_by: 'jean-luc-passave'
        }]
    });
});

// Fonction principale
async function main() {
    console.log('🚀 Démarrage du test...');
    
    // Chargement de JARVIS
    const jarvisOk = await chargerJarvis();
    
    if (!jarvisOk) {
        console.log('⚠️  JARVIS non disponible - mode test');
    }

    // Démarrage du serveur
    app.listen(PORT, HOST, () => {
        console.log(`\n🌐 Serveur démarré sur http://${HOST}:${PORT}`);
        console.log('\n📋 INSTRUCTIONS :');
        console.log('1. Ouvrez LM Studio');
        console.log('2. Allez dans "Local Server"');
        console.log(`3. URL: http://${HOST}:${PORT}`);
        console.log('4. Modèle: jarvis-brain-system');
        console.log('5. Chattez avec JARVIS !');
        console.log('\n🧠 JARVIS accessible via LM Studio !');
    });
}

// Gestion arrêt propre
process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt...');
    if (jarvisAgent?.shutdown) {
        await jarvisAgent.shutdown();
    }
    process.exit(0);
});

// Démarrage
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main, chargerJarvis };
