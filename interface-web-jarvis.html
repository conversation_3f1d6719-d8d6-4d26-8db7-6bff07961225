<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 JARVIS - Interface Web</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: #2d2d2d;
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #404040;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .status {
            color: #00ff88;
            font-size: 14px;
        }

        .chat-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }

        .message {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
        }

        .message.user {
            background: #007AFF;
            margin-left: auto;
            text-align: right;
        }

        .message.assistant {
            background: #2d2d2d;
            border: 1px solid #404040;
        }

        .input-container {
            background: #2d2d2d;
            padding: 20px;
            border-top: 1px solid #404040;
            display: flex;
            gap: 10px;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }

        #messageInput {
            flex: 1;
            background: #1a1a1a;
            border: 1px solid #404040;
            border-radius: 8px;
            padding: 12px;
            color: #ffffff;
            font-size: 16px;
        }

        #messageInput:focus {
            outline: none;
            border-color: #007AFF;
        }

        #sendButton {
            background: #007AFF;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            color: white;
            font-size: 16px;
            cursor: pointer;
        }

        #sendButton:hover {
            background: #0056CC;
        }

        .loading {
            display: none;
            text-align: center;
            color: #888;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 JARVIS Brain System</h1>
        <div class="status">✅ Connecté - QI: 373+ - 86B+ neurones actifs</div>
    </div>

    <div class="chat-container" id="chatContainer">
        <div class="message assistant">
            <strong>🧠 JARVIS:</strong><br>
            Bonjour Jean-Luc ! Je suis ton cerveau artificiel personnel. Mes 86 milliards de neurones sont prêts à t'aider. Comment puis-je t'assister aujourd'hui ?
        </div>
    </div>

    <div class="loading" id="loading">
        🧠 JARVIS réfléchit avec ses 86 milliards de neurones...
    </div>

    <div class="input-container">
        <input type="text" id="messageInput" placeholder="Parlez à JARVIS..." />
        <button id="sendButton">Envoyer</button>
    </div>

    <script>
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const loading = document.getElementById('loading');

        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Afficher le message utilisateur
            addMessage('user', message);
            messageInput.value = '';

            // Afficher le loading
            loading.style.display = 'block';

            try {
                // Appel à l'API JARVIS
                const response = await fetch('http://localhost:1234/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: 'jarvis-brain-system',
                        messages: [
                            { role: 'user', content: message }
                        ]
                    })
                });

                const data = await response.json();
                
                // Masquer le loading
                loading.style.display = 'none';

                if (data.choices && data.choices[0]) {
                    const jarvisResponse = data.choices[0].message.content;
                    
                    // Si c'est un objet complexe, extraire le message
                    let displayMessage = jarvisResponse;
                    if (typeof jarvisResponse === 'object' && jarvisResponse.message) {
                        displayMessage = jarvisResponse.message;
                    }
                    
                    addMessage('assistant', displayMessage);
                } else {
                    addMessage('assistant', 'Erreur de communication avec JARVIS');
                }

            } catch (error) {
                loading.style.display = 'none';
                addMessage('assistant', 'Erreur de connexion: ' + error.message);
            }
        }

        function addMessage(sender, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            if (sender === 'user') {
                messageDiv.innerHTML = `<strong>👤 Jean-Luc:</strong><br>${content}`;
            } else {
                messageDiv.innerHTML = `<strong>🧠 JARVIS:</strong><br>${content}`;
            }

            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Focus sur l'input
        messageInput.focus();
    </script>
</body>
</html>
