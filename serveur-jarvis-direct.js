#!/usr/bin/env node

/**
 * 🧠 SERVEUR JARVIS DIRECT - SANS INTERMÉDIAIRE
 * 
 * Connexion directe à l'agent JARVIS sans aucun intermédiaire
 * Jean-Luc PASSAVE - 2025
 */

const http = require('http');
const url = require('url');

console.log('🧠 SERVEUR JARVIS DIRECT - SANS INTERMÉDIAIRE');
console.log('============================================');

// Configuration
const PORT = 1234;
const HOST = 'localhost';

// Variables
let jarvisAgent = null;
let isJarvisReady = false;

// Chargement direct de JARVIS
async function chargerJarvisDirectement() {
    try {
        console.log('🧠 Chargement DIRECT de l\'agent JARVIS...');
        
        // Chargement direct du module
        const AgentModule = require('/Volumes/seagate/JARVIS_BRAIN_SYSTEM/core/deepseek-r1-agent-integrated.js');
        jarvisAgent = new AgentModule();
        
        console.log('🔄 Initialisation directe...');
        await jarvisAgent.initialize();
        
        isJarvisReady = true;
        console.log('✅ JARVIS prêt en mode DIRECT !');
        return true;
        
    } catch (error) {
        console.error('❌ Erreur JARVIS direct:', error.message);
        return false;
    }
}

// Interface web simple
const interfaceWeb = `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 JARVIS - Mode Direct</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #1a1a1a; color: #ffffff; height: 100vh; display: flex; flex-direction: column; }
        .header { background: #2d2d2d; padding: 20px; text-align: center; border-bottom: 1px solid #404040; }
        .header h1 { font-size: 24px; margin-bottom: 10px; }
        .status { color: #00ff88; font-size: 14px; }
        .chat-container { flex: 1; padding: 20px; overflow-y: auto; max-width: 800px; margin: 0 auto; width: 100%; }
        .message { margin-bottom: 20px; padding: 15px; border-radius: 10px; max-width: 80%; }
        .message.user { background: #007AFF; margin-left: auto; text-align: right; }
        .message.assistant { background: #2d2d2d; border: 1px solid #404040; }
        .input-container { background: #2d2d2d; padding: 20px; border-top: 1px solid #404040; display: flex; gap: 10px; max-width: 800px; margin: 0 auto; width: 100%; }
        #messageInput { flex: 1; background: #1a1a1a; border: 1px solid #404040; border-radius: 8px; padding: 12px; color: #ffffff; font-size: 16px; }
        #messageInput:focus { outline: none; border-color: #007AFF; }
        #sendButton { background: #007AFF; border: none; border-radius: 8px; padding: 12px 24px; color: white; font-size: 16px; cursor: pointer; }
        #sendButton:hover { background: #0056CC; }
        .loading { display: none; text-align: center; color: #888; padding: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 JARVIS Brain System - Mode Direct</h1>
        <div class="status">✅ Connexion Directe - Aucun Intermédiaire</div>
    </div>
    <div class="chat-container" id="chatContainer">
        <div class="message assistant">
            <strong>🧠 JARVIS:</strong><br>
            Bonjour Jean-Luc ! Je suis ton agent JARVIS en connexion directe. Aucun intermédiaire, communication pure et authentique !
        </div>
    </div>
    <div class="loading" id="loading">🧠 JARVIS traite votre message...</div>
    <div class="input-container">
        <input type="text" id="messageInput" placeholder="Parlez directement à JARVIS..." />
        <button id="sendButton">Envoyer</button>
    </div>
    <script>
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const loading = document.getElementById('loading');

        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            addMessage('user', message);
            messageInput.value = '';
            loading.style.display = 'block';
            try {
                const response = await fetch('/direct', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });
                const data = await response.json();
                loading.style.display = 'none';
                if (data.response) {
                    addMessage('assistant', data.response);
                } else if (data.error) {
                    addMessage('assistant', 'Erreur: ' + data.error);
                } else {
                    addMessage('assistant', 'Erreur de communication');
                }
            } catch (error) {
                loading.style.display = 'none';
                addMessage('assistant', 'Erreur de connexion: ' + error.message);
            }
        }

        function addMessage(sender, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = \`message \${sender}\`;
            if (sender === 'user') {
                messageDiv.innerHTML = \`<strong>👤 Jean-Luc:</strong><br>\${content}\`;
            } else {
                messageDiv.innerHTML = \`<strong>🧠 JARVIS:</strong><br>\${content}\`;
            }
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') { sendMessage(); }
        });
        messageInput.focus();
    </script>
</body>
</html>`;

// Serveur HTTP
const server = http.createServer(async (req, res) => {
    // CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    const parsedUrl = url.parse(req.url, true);
    
    // Route principale - Interface web
    if (parsedUrl.pathname === '/' || parsedUrl.pathname === '/index.html') {
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(interfaceWeb);
        return;
    }
    
    // Route de test
    if (parsedUrl.pathname === '/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            status: 'ok',
            jarvis_ready: isJarvisReady,
            mode: 'DIRECT - Aucun intermédiaire',
            timestamp: new Date().toISOString()
        }));
        return;
    }
    
    // Route directe JARVIS
    if (parsedUrl.pathname === '/direct' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', async () => {
            try {
                console.log('💬 Message direct reçu');
                
                if (!isJarvisReady) {
                    res.writeHead(503, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({
                        error: 'JARVIS non prêt'
                    }));
                    return;
                }

                const data = JSON.parse(body);
                const userMessage = data.message;
                
                console.log('👤 Message:', userMessage);

                // Appel DIRECT à JARVIS - AUCUN INTERMÉDIAIRE
                const startTime = Date.now();
                let response = null;

                try {
                    // Appel direct à la méthode processMessage
                    response = await jarvisAgent.processMessage(userMessage);
                    
                    console.log('🔍 Type de réponse:', typeof response);
                    console.log('🔍 Réponse brute:', response);
                    
                    // Extraire la vraie réponse si c'est un objet
                    if (typeof response === 'object' && response !== null) {
                        if (response.message) {
                            response = response.message;
                        } else if (response.response) {
                            response = response.response;
                        } else {
                            // Prendre la première propriété string
                            const keys = Object.keys(response);
                            for (const key of keys) {
                                if (typeof response[key] === 'string' && response[key].length > 0) {
                                    response = response[key];
                                    break;
                                }
                            }
                        }
                    }
                    
                    // S'assurer que c'est une string
                    response = String(response);
                    
                } catch (error) {
                    console.error('❌ Erreur appel direct JARVIS:', error);
                    response = `Erreur JARVIS direct: ${error.message}`;
                }

                const responseTime = Date.now() - startTime;
                console.log(`🧠 Réponse directe en ${responseTime}ms:`, response.substring(0, 100) + '...');

                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ response: response }));

            } catch (error) {
                console.error('❌ Erreur serveur direct:', error);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    error: error.message
                }));
            }
        });
        return;
    }
    
    // 404 pour autres routes
    res.writeHead(404);
    res.end('Not Found');
});

// Fonction principale
async function main() {
    console.log('🚀 Démarrage du serveur JARVIS DIRECT...');
    
    // Chargement direct de JARVIS
    const jarvisOk = await chargerJarvisDirectement();
    if (!jarvisOk) {
        console.log('❌ JARVIS non disponible - arrêt');
        process.exit(1);
    }
    
    // Démarrage du serveur
    server.listen(PORT, HOST, () => {
        console.log(`\n🌐 Serveur JARVIS DIRECT actif sur http://${HOST}:${PORT}`);
        console.log('\n📋 INSTRUCTIONS :');
        console.log('1. Ouvrez votre navigateur');
        console.log(`2. Allez sur http://${HOST}:${PORT}`);
        console.log('3. Chattez DIRECTEMENT avec JARVIS !');
        console.log('\n🧠 JARVIS en mode DIRECT - Aucun intermédiaire !');
    });
}

// Arrêt propre
process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt du serveur direct...');
    if (jarvisAgent && jarvisAgent.shutdown) {
        try {
            await jarvisAgent.shutdown();
            console.log('✅ JARVIS arrêté proprement');
        } catch (error) {
            console.error('❌ Erreur arrêt JARVIS:', error);
        }
    }
    process.exit(0);
});

// Démarrage
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main, chargerJarvisDirectement };
