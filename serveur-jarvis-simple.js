#!/usr/bin/env node

/**
 * 🧠 SERVEUR JARVIS SIMPLE AVEC INTERFACE WEB
 * 
 * Serveur qui expose JARVIS avec interface web intégrée
 * Jean-Luc PASSAVE - 2025
 */

const http = require('http');
const url = require('url');

console.log('🧠 SERVEUR JARVIS SIMPLE AVEC INTERFACE WEB');
console.log('==========================================');

// Configuration
const PORT = 1234;
const HOST = 'localhost';
const JARVIS_AGENT_PATH = '/Volumes/seagate/JARVIS_BRAIN_SYSTEM/core/deepseek-r1-agent-integrated.js';

// Variables
let jarvisAgent = null;
let isJarvisReady = false;

// Chargement de JARVIS avec connexion à la vraie mémoire
async function chargerJarvis() {
    try {
        console.log('🧠 Chargement de l\'agent JARVIS...');
        console.log('📂 Chemin:', JARVIS_AGENT_PATH);

        // Changer vers le répertoire principal pour accéder à la vraie mémoire
        const originalCwd = process.cwd();
        process.chdir('/Volumes/seagate/JARVIS_BRAIN_SYSTEM');
        console.log('📂 Répertoire changé vers:', process.cwd());

        const AgentModule = require(JARVIS_AGENT_PATH);
        jarvisAgent = new AgentModule();

        console.log('🔄 Initialisation avec vraie mémoire...');
        await jarvisAgent.initialize();

        // Revenir au répertoire original
        process.chdir(originalCwd);

        isJarvisReady = true;
        console.log('✅ JARVIS prêt avec vraie mémoire !');
        return true;

    } catch (error) {
        console.error('❌ Erreur JARVIS:', error.message);
        return false;
    }
}

// Interface web intégrée
const interfaceWeb = `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 JARVIS - Interface Web</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #1a1a1a; color: #ffffff; height: 100vh; display: flex; flex-direction: column; }
        .header { background: #2d2d2d; padding: 20px; text-align: center; border-bottom: 1px solid #404040; }
        .header h1 { font-size: 24px; margin-bottom: 10px; }
        .status { color: #00ff88; font-size: 14px; }
        .chat-container { flex: 1; padding: 20px; overflow-y: auto; max-width: 800px; margin: 0 auto; width: 100%; }
        .message { margin-bottom: 20px; padding: 15px; border-radius: 10px; max-width: 80%; }
        .message.user { background: #007AFF; margin-left: auto; text-align: right; }
        .message.assistant { background: #2d2d2d; border: 1px solid #404040; }
        .input-container { background: #2d2d2d; padding: 20px; border-top: 1px solid #404040; display: flex; gap: 10px; max-width: 800px; margin: 0 auto; width: 100%; }
        #messageInput { flex: 1; background: #1a1a1a; border: 1px solid #404040; border-radius: 8px; padding: 12px; color: #ffffff; font-size: 16px; }
        #messageInput:focus { outline: none; border-color: #007AFF; }
        #sendButton { background: #007AFF; border: none; border-radius: 8px; padding: 12px 24px; color: white; font-size: 16px; cursor: pointer; }
        #sendButton:hover { background: #0056CC; }
        .loading { display: none; text-align: center; color: #888; padding: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 JARVIS Brain System</h1>
        <div class="status">✅ Connecté - QI: 462+ - 86B+ neurones actifs</div>
    </div>
    <div class="chat-container" id="chatContainer">
        <div class="message assistant">
            <strong>🧠 JARVIS:</strong><br>
            Bonjour Jean-Luc ! Je suis ton cerveau artificiel personnel. Mes 86+ milliards de neurones sont prêts à t'aider. Comment puis-je t'assister aujourd'hui ?
        </div>
    </div>
    <div class="loading" id="loading">🧠 JARVIS réfléchit avec ses 86+ milliards de neurones...</div>
    <div class="input-container">
        <input type="text" id="messageInput" placeholder="Parlez à JARVIS..." />
        <button id="sendButton">Envoyer</button>
    </div>
    <script>
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const loading = document.getElementById('loading');

        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            addMessage('user', message);
            messageInput.value = '';
            loading.style.display = 'block';
            try {
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });
                const data = await response.json();
                loading.style.display = 'none';
                if (data.response) {
                    addMessage('assistant', data.response);
                } else {
                    addMessage('assistant', 'Erreur de communication avec JARVIS');
                }
            } catch (error) {
                loading.style.display = 'none';
                addMessage('assistant', 'Erreur de connexion: ' + error.message);
            }
        }

        function addMessage(sender, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = \`message \${sender}\`;
            if (sender === 'user') {
                messageDiv.innerHTML = \`<strong>👤 Jean-Luc:</strong><br>\${content}\`;
            } else {
                messageDiv.innerHTML = \`<strong>🧠 JARVIS:</strong><br>\${content}\`;
            }
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') { sendMessage(); }
        });
        messageInput.focus();
    </script>
</body>
</html>`;

// Serveur HTTP
const server = http.createServer(async (req, res) => {
    // CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    const parsedUrl = url.parse(req.url, true);
    
    // Route principale - Interface web
    if (parsedUrl.pathname === '/' || parsedUrl.pathname === '/index.html') {
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(interfaceWeb);
        return;
    }
    
    // Route de test
    if (parsedUrl.pathname === '/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            status: 'ok',
            jarvis_ready: isJarvisReady,
            model: 'JARVIS Brain System',
            timestamp: new Date().toISOString()
        }));
        return;
    }
    
    // Route chat simple
    if (parsedUrl.pathname === '/chat' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', async () => {
            try {
                console.log('💬 Requête chat reçue');
                
                if (!isJarvisReady) {
                    res.writeHead(503, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({
                        error: 'JARVIS non prêt'
                    }));
                    return;
                }

                const data = JSON.parse(body);
                const userMessage = data.message;
                
                console.log('👤 Message:', userMessage);

                // Appel direct à JARVIS via Ollama - AUCUNE SIMULATION
                const startTime = Date.now();
                let response = null;

                try {
                    // Essayer d'abord l'agent JARVIS local
                    if (jarvisAgent && jarvisAgent.processMessage) {
                        response = await jarvisAgent.processMessage(userMessage);
                    } else if (jarvisAgent && jarvisAgent.chat) {
                        response = await jarvisAgent.chat(userMessage);
                    } else {
                        // Fallback vers Ollama si l'agent local ne fonctionne pas
                        console.log('🔄 Fallback vers Ollama...');
                        const ollamaResponse = await fetch('http://localhost:11434/api/generate', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                model: 'claude-in-deepseek:latest',
                                prompt: `Tu es JARVIS, l'assistant personnel de Jean-Luc PASSAVE. Réponds de manière authentique et personnelle.\n\nJean-Luc: ${userMessage}\nJARVIS:`,
                                stream: false
                            })
                        });

                        if (ollamaResponse.ok) {
                            const ollamaData = await ollamaResponse.json();
                            response = ollamaData.response;
                            console.log('✅ Réponse obtenue via Ollama');
                        } else {
                            throw new Error('Ollama non disponible');
                        }
                    }

                    if (!response) {
                        throw new Error('Agent JARVIS a retourné une réponse vide');
                    }

                    // Extraire le texte si c'est un objet
                    if (typeof response === 'object') {
                        if (response.message) {
                            response = response.message;
                        } else if (response.content) {
                            response = response.content;
                        } else if (response.text) {
                            response = response.text;
                        } else if (response.response) {
                            response = response.response;
                        } else {
                            // Si c'est un objet complexe, prendre la première propriété string
                            const keys = Object.keys(response);
                            for (const key of keys) {
                                if (typeof response[key] === 'string' && response[key].length > 0) {
                                    response = response[key];
                                    break;
                                }
                            }
                            // Si toujours un objet, le convertir en string lisible
                            if (typeof response === 'object') {
                                response = JSON.stringify(response, null, 2);
                            }
                        }
                    }

                    // S'assurer que c'est une string
                    response = String(response);

                } catch (error) {
                    console.error('❌ Erreur appel JARVIS:', error);
                    response = `Erreur JARVIS: ${error.message}`;
                }

                const responseTime = Date.now() - startTime;
                console.log(`🧠 Réponse en ${responseTime}ms:`, String(response).substring(0, 100) + '...');

                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ response: response }));

            } catch (error) {
                console.error('❌ Erreur serveur:', error);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    error: error.message
                }));
            }
        });
        return;
    }
    
    // 404 pour autres routes
    res.writeHead(404);
    res.end('Not Found');
});

// Fonction principale
async function main() {
    console.log('🚀 Démarrage du serveur JARVIS...');
    
    // Chargement de JARVIS
    const jarvisOk = await chargerJarvis();
    if (!jarvisOk) {
        console.log('⚠️  JARVIS non disponible - mode test');
    }
    
    // Démarrage du serveur
    server.listen(PORT, HOST, () => {
        console.log(`\n🌐 Serveur JARVIS actif sur http://${HOST}:${PORT}`);
        console.log('\n📋 INSTRUCTIONS :');
        console.log('1. Ouvrez votre navigateur');
        console.log(`2. Allez sur http://${HOST}:${PORT}`);
        console.log('3. Chattez avec JARVIS !');
        console.log('\n🧠 JARVIS accessible via interface web !');
        console.log('\n🔗 Test de connexion: http://localhost:1234/health');
    });
}

// Arrêt propre
process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt du serveur...');
    if (jarvisAgent && jarvisAgent.shutdown) {
        try {
            await jarvisAgent.shutdown();
            console.log('✅ JARVIS arrêté proprement');
        } catch (error) {
            console.error('❌ Erreur arrêt JARVIS:', error);
        }
    }
    process.exit(0);
});

// Démarrage
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main, chargerJarvis };
