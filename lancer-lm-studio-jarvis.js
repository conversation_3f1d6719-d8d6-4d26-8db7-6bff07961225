#!/usr/bin/env node

/**
 * 🚀 LANCEUR LM STUDIO + JARVIS
 * Lance LM Studio avec l'agent JARVIS intégré
 * 
 * <PERSON><PERSON><PERSON> PASSAVE - 2025
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 LANCEMENT LM STUDIO + JARVIS');
console.log('===============================');

const lmStudioPath = path.join(__dirname, 'lm_studio_local', 'LM Studio.app', 'Contents', 'MacOS', 'LM Studio');

console.log('📂 Chemin LM Studio:', lmStudioPath);
console.log('🧠 Agent JARVIS intégré');
console.log('🚀 Lancement...');

const lmStudio = spawn(lmStudioPath, [], {
    stdio: 'inherit',
    detached: false
});

lmStudio.on('close', (code) => {
    console.log(`✅ LM Studio fermé avec le code: ${code}`);
});

lmStudio.on('error', (error) => {
    console.error('❌ Erreur lancement LM Studio:', error);
});

console.log('🧠 LM Studio + JARVIS démarré !');
console.log('💬 Vous pouvez maintenant chatter avec JARVIS via l\'interface LM Studio');
