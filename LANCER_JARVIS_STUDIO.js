#!/usr/bin/env node

/**
 * 🚀 LANCEUR JARVIS STUDIO
 * 
 * Interface LM Studio avec JARVIS intégré directement
 * AUCUNE SIMULATION - Connexion directe au cerveau artificiel
 * 
 * Jean-Luc PASSAVE - 2025
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🧠 JARVIS STUDIO - LANCEUR');
console.log('==========================');
console.log('💙 Interface LM Studio avec cerveau artificiel intégré');
console.log('🚀 Zéro latence - Réflexion instantanée\n');

// Vérification des prérequis
function verifierPrerequisites() {
    console.log('🔍 Vérification des prérequis...');
    
    // Vérification de Node.js
    const nodeVersion = process.version;
    console.log(`✅ Node.js: ${nodeVersion}`);
    
    // Vérification des fichiers essentiels
    const fichiersEssentiels = [
        'core/deepseek-r1-agent-integrated.js',
        'thermal_memory_persistent.json',
        'interface/jarvis-lm-studio-interface.html',
        'interface/jarvis-studio-electron.js',
        'interface/package.json'
    ];
    
    let manquants = [];
    
    for (const fichier of fichiersEssentiels) {
        if (fs.existsSync(fichier)) {
            console.log(`✅ ${fichier}`);
        } else {
            console.log(`❌ ${fichier} - MANQUANT`);
            manquants.push(fichier);
        }
    }
    
    if (manquants.length > 0) {
        console.log('\n❌ ERREUR: Fichiers manquants détectés');
        console.log('🔧 Veuillez vérifier l\'installation');
        process.exit(1);
    }
    
    console.log('✅ Tous les prérequis sont satisfaits\n');
}

// Installation des dépendances Electron si nécessaire
async function installerDependances() {
    const interfaceDir = path.join(__dirname, 'interface');
    const nodeModulesPath = path.join(interfaceDir, 'node_modules');
    
    if (!fs.existsSync(nodeModulesPath)) {
        console.log('📦 Installation des dépendances Electron...');
        
        return new Promise((resolve, reject) => {
            const npm = spawn('npm', ['install'], {
                cwd: interfaceDir,
                stdio: 'inherit'
            });
            
            npm.on('close', (code) => {
                if (code === 0) {
                    console.log('✅ Dépendances installées\n');
                    resolve();
                } else {
                    console.log('❌ Erreur installation dépendances');
                    reject(new Error(`npm install failed with code ${code}`));
                }
            });
        });
    } else {
        console.log('✅ Dépendances déjà installées\n');
    }
}

// Lancement de JARVIS Studio
function lancerJarvisStudio() {
    console.log('🚀 Lancement de JARVIS Studio...');
    
    const interfaceDir = path.join(__dirname, 'interface');
    
    const electron = spawn('npx', ['electron', '.'], {
        cwd: interfaceDir,
        stdio: 'inherit'
    });
    
    electron.on('close', (code) => {
        if (code === 0) {
            console.log('✅ JARVIS Studio fermé proprement');
        } else {
            console.log(`❌ JARVIS Studio fermé avec le code: ${code}`);
        }
    });
    
    electron.on('error', (error) => {
        console.error('❌ Erreur lancement JARVIS Studio:', error);
    });
    
    // Gestion de l'arrêt propre
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt demandé...');
        electron.kill('SIGTERM');
    });
}

// Fonction principale
async function main() {
    try {
        verifierPrerequisites();
        await installerDependances();
        lancerJarvisStudio();
        
    } catch (error) {
        console.error('❌ Erreur:', error.message);
        process.exit(1);
    }
}

// Gestion des erreurs
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non capturée:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason) => {
    console.error('❌ Promesse rejetée:', reason);
    process.exit(1);
});

// Lancement
if (require.main === module) {
    main();
}

module.exports = { main, verifierPrerequisites, installerDependances, lancerJarvisStudio };
